<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>自动填写费率</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="section">
            <h3>添加商品</h3>
            <div class="input-group">
                <textarea id="productInput" class="product-input" rows="4" placeholder="请输入商品名称，每行一个..."></textarea>
                <button id="addProductBtn" class="action-button">添加商品</button>
            </div>
        </div>

        <div class="divider"></div>

        <div class="section">
            <h3>费率设置</h3>
            <div class="rate-group">
                <label>高档费率：</label>
                <select id="highRate">
                    <option value="10">10%</option>
                    <option value="20">20%</option>
                    <option value="30">30%</option>
                    <option value="40" selected>40%</option>
                    <option value="50">50%</option>
                    <option value="60">60%</option>
                    <option value="70">70%</option>
                    <option value="80">80%</option>
                    <option value="90">90%</option>
                </select>
                <input type="number" id="highCount" value="5" min="1">
                <span>单</span>
            </div>
            
            <div class="rate-group">
                <label>低档费率：</label>
                <select id="lowRate">
                    <option value="10">10%</option>
                    <option value="20" selected>20%</option>
                    <option value="30">30%</option>
                    <option value="40">40%</option>
                    <option value="50">50%</option>
                </select>
            </div>

            <button id="startBtn" class="start-button">开始设置费率</button>
        </div>
    </div>
    <script src="popup.js"></script>
</body>
</html> 