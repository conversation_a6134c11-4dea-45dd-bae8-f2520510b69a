// 保存设置到Chrome存储
function saveSettings() {
    const highRate = document.getElementById('highRate').value;
    const highCount = document.getElementById('highCount').value;
    const lowRate = document.getElementById('lowRate').value;
    const productInput = document.getElementById('productInput').value;

    chrome.storage.sync.set({
        highRate: highRate,
        highCount: highCount,
        lowRate: lowRate,
        productInput: productInput
    });
}

// 从Chrome存储加载设置
function loadSettings() {
    chrome.storage.sync.get(['highRate', 'highCount', 'lowRate', 'productInput'], (result) => {
        if (result.highRate) {
            document.getElementById('highRate').value = result.highRate;
        }
        if (result.highCount) {
            document.getElementById('highCount').value = result.highCount;
        }
        if (result.lowRate) {
            document.getElementById('lowRate').value = result.lowRate;
        }
        if (result.productInput) {
            document.getElementById('productInput').value = result.productInput;
        }
    });
}

// 添加商品按钮点击事件
document.getElementById('addProductBtn').addEventListener('click', async () => {
    const productInputElement = document.getElementById('productInput');
    const productInput = productInputElement.value;
    const products = productInput.split('\n').filter(line => line.trim() !== '');

    if (products.length === 0) {
        alert('请输入商品名称，每行一个');
        return;
    }

    // 清空输入框
    productInputElement.value = '';
    // 保存清空后的状态到存储
    saveSettings();
    console.log('输入框已清空');

    // 向当前标签页注入执行命令
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab) {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: (products) => {
                // 模拟真实的鼠标点击
                function simulateClick(element) {
                    const rect = element.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;
                    
                    const mouseDown = new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        clientX: centerX,
                        clientY: centerY
                    });
                    const mouseUp = new MouseEvent('mouseup', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        clientX: centerX,
                        clientY: centerY
                    });
                    const click = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        clientX: centerX,
                        clientY: centerY
                    });
                    
                    element.dispatchEvent(mouseDown);
                    element.dispatchEvent(mouseUp);
                    element.dispatchEvent(click);
                }

                // 模拟真实的复制粘贴操作
                async function simulatePaste(element, text) {
                    // 首先确保元素获得焦点
                    element.focus();
                    element.click();
                    await new Promise(r => setTimeout(r, 300 + Math.random() * 200));

                    // 清空现有内容
                    element.value = '';
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    await new Promise(r => setTimeout(r, 100 + Math.random() * 100));

                    // 创建剪贴板事件
                    const pasteEvent = new ClipboardEvent('paste', {
                        bubbles: true,
                        cancelable: true,
                        clipboardData: new DataTransfer()
                    });
                    
                    // 设置文本
                    element.value = text;
                    
                    // 触发事件序列
                    element.dispatchEvent(pasteEvent);
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    await new Promise(r => setTimeout(r, 50 + Math.random() * 50));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                }

                // 通过XPath查找元素
                function findElementByXPath(xpath) {
                    return document.evaluate(
                        xpath,
                        document,
                        null,
                        XPathResult.FIRST_ORDERED_NODE_TYPE,
                        null
                    ).singleNodeValue;
                }

                // 等待元素出现
                async function waitForElementXPath(xpath, timeout = 5000) {
                    const startTime = Date.now();
                    while (Date.now() - startTime < timeout) {
                        const element = findElementByXPath(xpath);
                        if (element) {
                            return element;
                        }
                        await new Promise(r => setTimeout(r, 100));
                    }
                    throw new Error(`等待元素超时: ${xpath}`);
                }

                // 添加随机延迟
                function randomDelay(min, max) {
                    return new Promise(r => setTimeout(r, min + Math.random() * (max - min)));
                }

                // 主函数
                async function addProducts() {
                    try {
                        // 点击添加商品按钮
                        console.log('等待添加商品按钮...');
                        const addButton = await waitForElementXPath("//button[contains(., '+点击添加')]");
                        console.log('点击添加商品按钮');
                        simulateClick(addButton);
                        await randomDelay(1800, 2200);

                        // 点击商品ID文本
                        console.log('等待商品ID文本...');
                        const idText = await waitForElementXPath("//span[contains(text(), '商品ID')]");
                        console.log('点击商品ID文本');
                        simulateClick(idText);
                        await randomDelay(1300, 1700);

                        // 点击商品名称选项
                        console.log('等待商品名称选项...');
                        const nameOption = await waitForElementXPath("//div[contains(@class, 'mx-output-link') and .//span[contains(text(), '商品名称')]]");
                        console.log('点击商品名称选项');
                        simulateClick(nameOption);
                        await randomDelay(1300, 1700);

                        // 主循环：处理所有商品
                        for(let i = 0; i < products.length; i++) {
                            const product = products[i];
                            console.log(`处理商品 ${i + 1}/${products.length}: ${product}`);

                            // 查找输入框 - 使用更稳定的选择器
                            console.log('查找输入框...');
                            // 使用更稳定的选择器策略，基于功能特征而非易变的class名
                            let input = null;
                            const inputSelectors = [
                                // 策略1: 基于搜索功能特征 - 最稳定
                                "//div[contains(@mx-change, 'onSearchChange')]//input[@placeholder='请输入' and not(@maxlength)]",
                                // 策略2: 基于与下拉框的组合关系
                                "//div[contains(@mx-view, 'searchWidth')]//input[@placeholder='请输入']",
                                // 策略3: 基于父容器的搜索功能，排除有字符限制的输入框
                                "//div[contains(@mx-change, 'onSearchChange')]//input[@type='text' and not(@maxlength)]",
                                // 策略4: 通过ID模式匹配，但排除有预设值的
                                "//input[contains(@id, '_input') and @placeholder='请输入' and not(@maxlength) and not(@value) or @value='']",
                                // 策略5: 备用 - 原有的class选择器（保持兼容性）
                                "//div[contains(@mx-change, 'onSearchChange')]//input[contains(@class, 'dZGuKQeSnC')]",
                                "//div[contains(@mx-change, 'onSearchChange')]//input[contains(@class, 'dZGuKQnknB')]",
                                "//div[contains(@mx-change, 'onSearchChange')]//input[contains(@class, 'dZGuKQcpnB')]"
                            ];

                            for (const selector of inputSelectors) {
                                try {
                                    input = await waitForElementXPath(selector, 2000); // 缩短等待时间
                                    if (input) {
                                        // 额外验证：确保不是表单字段（避免选中错误的输入框）
                                        const hasMaxLength = input.hasAttribute('maxlength');
                                        const isInSearchContainer = input.closest('div[mx-change*="onSearchChange"]');
                                        const isFormField = input.closest('div[class*="form-content"]') || input.closest('div[class*="custom-item"]');

                                        // 搜索输入框的特征：在搜索容器内 + 没有maxlength + 不是表单字段
                                        if (!hasMaxLength && isInSearchContainer && !isFormField) {
                                            console.log(`成功找到搜索输入框，使用选择器: ${selector}`);
                                            break;
                                        } else {
                                            console.log(`跳过非搜索输入框: maxLength=${hasMaxLength}, inSearchContainer=${!!isInSearchContainer}, isFormField=${!!isFormField}`);
                                            input = null;
                                        }
                                    }
                                } catch (e) {
                                    console.log(`选择器失败: ${selector}`);
                                }
                            }
                            if(!input) {
                                throw new Error('未找到搜索输入框');
                            }

                            // 确保输入框可见并且可以交互
                            console.log('聚焦输入框...');
                            input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            await randomDelay(400, 600);
                            
                            // 模拟复制粘贴操作
                            console.log('粘贴商品名称');
                            await simulatePaste(input, product);
                            
                            console.log('等待搜索结果...');
                            await randomDelay(1800, 2200);

                            // 查找并点击添加按钮
                            console.log('查找匹配商品的添加按钮...');
                            const addItemSelectors = [
                                // === 优先使用通用模糊匹配选择器 ===
                                `//tr[contains(., '${product}')]//span[contains(text(), '添加')]`,
                                `//tr[contains(., '${product}')]//td//span[contains(text(), '添加')]`,
                                
                                // === 精确匹配新版本"添加商品"文本的选择器 ===
                                // 兼容新版本"添加商品"文本
                                `//tr[contains(., '${product}')]//span[contains(@class, 'color-brand') and contains(@class, 'cursor-pointer') and text()='添加商品']`,
                                `//tr[contains(., '${product}')]//span[contains(@class, 'color-brand') and text()='添加商品']`,
                                `//tr[contains(., '${product}')]//span[contains(@class, 'cursor-pointer') and text()='添加商品']`,
                                
                                // 兼容td结构的选择器
                                `//tr[contains(., '${product}')]//td//span[text()='添加商品']`,
                                `//tr[contains(., '${product}')]//td//span[contains(@class, 'color-brand') and text()='添加商品']`,
                                `//tr[contains(., '${product}')]//td//span[contains(@class, 'color-brand') and contains(@class, 'cursor-pointer') and text()='添加商品']`,
                                
                                // === 保持原有选择器兼容旧版本 ===
                                // 新版本：button包裹span结构
                                `//tr[contains(., '${product}')]//button//span[text()='添加']`,
                                `//tr[contains(., '${product}')]//button[contains(@class, 'dZGuKQ')]//span[text()='添加']`,
                                // 兼容旧版本：直接span结构
                                `//tr[contains(., '${product}')]//span[contains(@class, 'color-brand') and text()='添加']`,
                                // 通用备选：任何包含"添加"文本的可点击元素
                                `//tr[contains(., '${product}')]//*[text()='添加' and (self::button or self::span)]`
                            ];

                            let addItemButton = null;
                            for (const selector of addItemSelectors) {
                                try {
                                    addItemButton = await waitForElementXPath(selector, 2000);
                                    if (addItemButton) {
                                        console.log(`✅ 找到添加按钮，使用选择器: ${selector}`);
                                        console.log(`按钮元素信息:`, addItemButton.outerHTML);
                                        break;
                                    }
                                } catch (e) {
                                    console.log(`❌ 选择器失败: ${selector}`);
                                }
                            }
                            
                            if(!addItemButton) {
                                throw new Error(`未找到商品: ${product}`);
                            }

                            console.log('点击添加按钮');
                            simulateClick(addItemButton);
                            await randomDelay(1300, 1700);
                        }

                        console.log('所有商品添加完成');

                    } catch(error) {
                        console.error('执行过程中发生错误，停止操作:', error);
                        throw error;
                    }
                }

                // 执行添加商品
                console.log('开始执行自动添加商品...');
                addProducts().catch(error => {
                    console.error('执行失败:', error);
                });
            },
            args: [products]
        });
    }
});

// 开始执行按钮点击事件（费率设置功能）
document.getElementById('startBtn').addEventListener('click', async () => {
    // 获取当前设置
    const highRate = document.getElementById('highRate').value;
    const highCount = document.getElementById('highCount').value;
    const lowRate = document.getElementById('lowRate').value;

    // 向当前标签页注入执行命令
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab) {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: (highRate, highCount, lowRate) => {
                // 自动填写函数
                async function autoFillRates() {
                    console.log('开始执行自动填写任务...');

                    // 统计阶段
                    const style1Forms = document.querySelectorAll('td[mxv][mx-stickytable-shadow]');
                    const style2Forms = document.querySelectorAll('div[class*="dis-f flex-column"][style*="background-color: #f7f8fa"]');

                    const style1Count = Array.from(style1Forms).filter(form => 
                        form.textContent.includes('高档费率') && form.textContent.includes('限5单')
                    ).length;

                    const style2Count = Array.from(style2Forms).filter(form => 
                        form.textContent.includes('高档费率') && form.textContent.includes('去设置')
                    ).length;

                    console.log(`统计结果：
                        样式一商品数量：${style1Count}个
                        样式二商品数量：${style2Count}个
                        总共需要处理：${style1Count + style2Count}个商品
                    `);

                    // 智能处理策略：
                    // 1. 如果只有样式一商品 -> 直接处理样式一
                    // 2. 如果有样式一+样式二商品 -> 跳过样式一，直接处理样式二（样式二会处理所有商品）
                    const shouldProcessStyle1 = style1Count > 0 && style2Count === 0;
                    const shouldProcessStyle2 = style2Count > 0;

                    if (shouldProcessStyle1) {
                        console.log('🎯 检测到只有样式一商品，使用样式一处理方式...');
                    } else if (shouldProcessStyle2) {
                        if (style1Count > 0) {
                            console.log('🎯 检测到混合商品类型，跳过样式一处理，直接使用样式二处理所有商品...');
                        } else {
                            console.log('🎯 检测到只有样式二商品，使用样式二处理方式...');
                        }
                    }

                    // 第一阶段：处理样式一商品（仅当没有样式二商品时）
                    if (shouldProcessStyle1) {
                        console.log('开始处理样式一商品...');
                        let processedCount = 0;

                        for (const form of style1Forms) {
                            if (form.textContent.includes('高档费率') && form.textContent.includes('限5单')) {
                                processedCount++;
                                console.log(`正在处理第${processedCount}个样式一商品...`);

                                // 移除滚动功能，直接处理商品
                                console.log('开始处理当前样式一商品...');

                                // 点击页面空白处关闭其他下拉框
                                document.body.click();
                                await new Promise(resolve => setTimeout(resolve, 500));

                                // 使用样式二的通用选择器处理样式一商品
                                console.log('开始处理当前样式一商品...');

                                // 清理页面状态
                                console.log('清理页面状态...');
                                for (let i = 0; i < 3; i++) {
                                    document.body.click();
                                    await new Promise(resolve => setTimeout(resolve, 100));
                                }
                                await new Promise(resolve => setTimeout(resolve, 200));

                                // 检查并关闭任何可见的下拉菜单
                                const visibleDropdowns = document.querySelectorAll('div.mx-output-list:not([style*="display: none"])');
                                if (visibleDropdowns.length > 0) {
                                    console.log(`发现${visibleDropdowns.length}个未关闭的下拉菜单，强制关闭...`);
                                    document.body.click();
                                    await new Promise(resolve => setTimeout(resolve, 300));
                                }

                                // 1. 处理高档费率 - 使用通用查找函数
                                const highRateDropdown = findRateDropdown(form, 'High');
                                if (highRateDropdown) {
                                    // 获取下拉框ID，支持多种ID格式
                                    let dropdownId = '';
                                    if (highRateDropdown.id) {
                                        dropdownId = highRateDropdown.id.replace('toggle_', '');
                                    } else {
                                        // 尝试从父元素获取ID
                                        const parentWithId = highRateDropdown.closest('div[id*="ladderHighRatio"]');
                                        dropdownId = parentWithId ? parentWithId.id : 'ladderHighRatio';
                                    }
                                    console.log('找到高档费率下拉框，ID:', dropdownId);

                                    console.log('准备点击高档费率下拉框...');
                                    document.body.click();
                                    await new Promise(resolve => setTimeout(resolve, 200));

                                    await simulateRealMouse(highRateDropdown);
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // 使用样式二的选项查找策略
                                    let found = false;
                                    const targetRate = highRate + '%';

                                    console.log(`查找高档费率选项: ${targetRate}, 下拉框ID: ${dropdownId}`);
                                    let options = document.querySelectorAll('div.mx-output-link');
                                    console.log(`策略1: 找到${options.length}个mx-output-link选项`);

                                    for (const option of options) {
                                        const rateSpans = option.querySelectorAll('span');
                                        const mxClick = option.getAttribute('mx-click');

                                        // 严格匹配：必须包含当前下拉框的完整ID
                                        if (mxClick && mxClick.includes(dropdownId)) {
                                            for (const span of rateSpans) {
                                                if (span.textContent.trim() === targetRate) {
                                                    console.log(`严格匹配找到目标选项: ${targetRate}, mx-click:`, mxClick);
                                                    await simulateRealMouse(option);
                                                    console.log('等待选项选择生效...');
                                                    await new Promise(resolve => setTimeout(resolve, 400));

                                                    // 验证选择是否成功
                                                    const selectedValue = highRateDropdown.textContent;
                                                    if (selectedValue.includes(targetRate)) {
                                                        console.log(`高档费率选择成功: ${targetRate}`);
                                                    } else {
                                                        console.log(`高档费率选择可能未生效，当前显示: ${selectedValue}`);
                                                    }

                                                    found = true;
                                                    break;
                                                }
                                            }
                                            if (found) break;
                                        }
                                    }

                                    console.log('高档费率设置完成，短暂停顿...');
                                    await new Promise(resolve => setTimeout(resolve, 100));
                                }

                                console.log('高档费率处理完成，准备处理低档费率...');
                                await new Promise(resolve => setTimeout(resolve, 100));

                                // 2. 处理低档费率 - 使用通用查找函数
                                const lowRateDropdown = findRateDropdown(form, 'Low');
                                if (lowRateDropdown) {
                                    // 获取下拉框ID，支持多种ID格式
                                    let dropdownId = '';
                                    if (lowRateDropdown.id) {
                                        dropdownId = lowRateDropdown.id.replace('toggle_', '');
                                    } else {
                                        // 尝试从父元素获取ID或使用默认值
                                        const parentWithId = lowRateDropdown.closest('div[mx-change*="ladderLowRatio"]');
                                        dropdownId = parentWithId ? parentWithId.id : 'ladderLowRatio';
                                    }
                                    console.log('找到低档费率下拉框，ID:', dropdownId);

                                    console.log('准备点击低档费率下拉框...');
                                    document.body.click();
                                    await new Promise(resolve => setTimeout(resolve, 300));

                                    await simulateRealMouse(lowRateDropdown);
                                    await new Promise(resolve => setTimeout(resolve, 500));

                                    // 使用样式二的选项查找策略
                                    let found = false;
                                    const targetRate = lowRate + '%';

                                    console.log(`查找低档费率选项: ${targetRate}, 下拉框ID: ${dropdownId}`);
                                    let options = document.querySelectorAll('div.mx-output-link');
                                    console.log(`策略1: 找到${options.length}个mx-output-link选项`);

                                    for (const option of options) {
                                        const rateSpans = option.querySelectorAll('span');
                                        const mxClick = option.getAttribute('mx-click');

                                        // 严格匹配：必须包含当前下拉框的完整ID
                                        if (mxClick && mxClick.includes(dropdownId)) {
                                            for (const span of rateSpans) {
                                                if (span.textContent.trim() === targetRate) {
                                                    console.log(`严格匹配找到目标选项: ${targetRate}, mx-click:`, mxClick);
                                                    await simulateRealMouse(option);
                                                    console.log('等待选项选择生效...');
                                                    await new Promise(resolve => setTimeout(resolve, 400));

                                                    // 验证选择是否成功
                                                    const selectedValue = lowRateDropdown.textContent;
                                                    if (selectedValue.includes(targetRate)) {
                                                        console.log(`低档费率选择成功: ${targetRate}`);
                                                    } else {
                                                        console.log(`低档费率选择可能未生效，当前显示: ${selectedValue}`);
                                                    }

                                                    found = true;
                                                    break;
                                                }
                                            }
                                            if (found) break;
                                        }
                                    }

                                    console.log('低档费率设置完成，短暂停顿...');
                                    await new Promise(resolve => setTimeout(resolve, 100));
                                }
                                
                                // 点击页面空白处关闭所有下拉框
                                document.body.click();
                                
                                // 等待当前商品处理完成
                                await new Promise(resolve => setTimeout(resolve, 800));
                            }
                        }
                        console.log(`样式一商品处理完成，共处理${processedCount}个`);
                    }

                    // 第二阶段：处理样式二商品（会处理所有商品）
                    if (shouldProcessStyle2) {
                        if (style1Count > 0 && style2Count > 0) {
                            console.log('开始处理样式二商品（将同时处理所有样式一和样式二商品）...');
                        } else {
                            console.log('开始处理样式二商品...');
                        }
                        // 清理全局SKU记录，开始新的处理周期
                        if (window.processedSkuIds) {
                            window.processedSkuIds.clear();
                            console.log('清理全局SKU记录');
                        }
                        let processedCount = 0;

                        for (const form of style2Forms) {
                            if (form.textContent.includes('高档费率') && form.textContent.includes('去设置')) {
                                processedCount++;
                                console.log(`正在处理第${processedCount}个样式二商品...`);

                                // 找到并点击"去设置"按钮
                                const setButton = form.querySelector('span.color-brand.cursor-pointer');
                                if (setButton) {
                                    // 移除滚动功能，直接点击按钮
                                    console.log('准备点击"去设置"按钮...');

                                    await simulateRealMouse(setButton);
                                    
                                    // 等待弹窗加载
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    
                                    // 先统计总页数
                                    const totalPages = getTotalPages();
                                    console.log(`📊 检测到总共 ${totalPages} 页需要处理`);

                                    // 按页面顺序处理所有SKU
                                    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
                                        console.log(`📄 开始处理第 ${currentPage}/${totalPages} 页`);

                                        // 如果不是第一页，需要跳转
                                        if (currentPage > 1) {
                                            const jumpSuccess = await goToPage(currentPage);
                                            if (!jumpSuccess) {
                                                console.log(`❌ 跳转到第 ${currentPage} 页失败，跳过`);
                                                continue;
                                            }
                                            console.log(`✅ 成功跳转到第 ${currentPage} 页`);
                                        }

                                        // 处理当前页的SKUs - 使用更稳定的选择器策略
                                        let skuForms = [];

                                        // 策略1: 查找费率设置容器（最稳定的方法）
                                        console.log('  策略1: 查找费率设置容器...');
                                        const rateContainers = document.querySelectorAll('div[style*="background-color: #f7f8fa"]');
                                        if (rateContainers.length > 0) {
                                            // 验证这些容器确实包含费率设置
                                            skuForms = Array.from(rateContainers).filter(container => {
                                                const hasHighRate = container.textContent.includes('高档费率');
                                                const hasLowRate = container.textContent.includes('低档费率');
                                                return hasHighRate && hasLowRate;
                                            });
                                            console.log(`  通过费率容器找到 ${skuForms.length} 个SKU`);
                                        }

                                        // 策略2: 如果策略1失败，使用mxa属性查找
                                        if (skuForms.length === 0) {
                                            console.log('  策略2: 使用mxa属性查找...');
                                            const mxaSelectors = [
                                                'div[mxa*="dZGuKQnkdt:x"]', // 新版本
                                                'div[mxa="dZGuKQcpdt:x"]'   // 旧版本
                                            ];

                                            for (const selector of mxaSelectors) {
                                                skuForms = document.querySelectorAll(selector);
                                                if (skuForms.length > 0) {
                                                    console.log(`  使用mxa选择器找到 ${skuForms.length} 个SKU: ${selector}`);
                                                    break;
                                                }
                                            }
                                        }

                                        // 策略3: 通过功能属性查找
                                        if (skuForms.length === 0) {
                                            console.log('  策略3: 通过功能属性查找...');
                                            const highRateElements = document.querySelectorAll('div[mx-change*="ladderHighRatioOnChange"]');
                                            if (highRateElements.length > 0) {
                                                skuForms = Array.from(highRateElements).map(element => {
                                                    // 查找包含完整费率设置的父容器
                                                    return element.closest('tr') || // 表格行
                                                           element.closest('div[style*="background-color: #f7f8fa"]') || // 费率容器
                                                           element.closest('td') || // 表格单元格
                                                           element.parentElement;
                                                }).filter(Boolean);
                                                console.log(`  通过功能属性找到 ${skuForms.length} 个SKU`);
                                            }
                                        }

                                        // 去重处理，避免重复处理相同的SKU - 最终改进版
                                        const uniqueSkuForms = [];

                                        // 使用全局变量跨页面跟踪已处理的SKU
                                        if (!window.processedSkuIds) {
                                            window.processedSkuIds = new Set();
                                        }

                                        console.log(`当前页面找到 ${skuForms.length} 个SKU容器`);

                                        for (let i = 0; i < skuForms.length; i++) {
                                            const skuForm = skuForms[i];
                                            let skuId = null;

                                            // 方法1: 通过表格行中的checkbox value获取SKU ID（最可靠）
                                            const row = skuForm.closest('tr');
                                            if (row) {
                                                const checkbox = row.querySelector('input[type="checkbox"][value]');
                                                if (checkbox && checkbox.value) {
                                                    skuId = `sku_${checkbox.value}`;
                                                    console.log(`  方法1成功: 通过checkbox获取SKU ID: ${skuId}`);
                                                }
                                            }

                                            // 方法2: 通过高档费率ID获取SKU ID
                                            if (!skuId) {
                                                const highRateElement = skuForm.querySelector('div[id*="ladderHighRatio"]');
                                                if (highRateElement && highRateElement.id) {
                                                    const match = highRateElement.id.match(/ladderHighRatio_(\d+)/);
                                                    if (match) {
                                                        skuId = `sku_${match[1]}`;
                                                        console.log(`  方法2成功: 通过高档费率ID获取SKU ID: ${skuId}`);
                                                    }
                                                }
                                            }

                                            // 方法3: 通过SKU价格输入框ID获取
                                            if (!skuId) {
                                                const priceInput = skuForm.querySelector('input[id*="sku_price"]');
                                                if (priceInput && priceInput.id) {
                                                    const match = priceInput.id.match(/sku_price_(\d+)/);
                                                    if (match) {
                                                        skuId = `sku_${match[1]}`;
                                                        console.log(`  方法3成功: 通过价格输入框获取SKU ID: ${skuId}`);
                                                    }
                                                }
                                            }

                                            // 方法4: 使用表格行的全局唯一标识
                                            if (!skuId) {
                                                if (row) {
                                                    // 获取行中的第二个td（SKU ID列）
                                                    const skuIdCell = row.children[1];
                                                    const cellText = skuIdCell ? skuIdCell.textContent.trim() : '';
                                                    if (cellText && cellText.length > 0 && /\d/.test(cellText)) {
                                                        skuId = `sku_${cellText}`;
                                                        console.log(`  方法4成功: 通过SKU ID列获取: ${skuId}`);
                                                    }
                                                }
                                            }

                                            // 方法5: 使用页面和位置组合作为最后手段
                                            if (!skuId) {
                                                skuId = `page_${currentPage}_index_${i}_${Date.now()}`;
                                                console.log(`  方法5备用: 使用位置标识: ${skuId}`);
                                            }

                                            // 验证SKU有效性 - 修复版
                                            const skuIdValue = skuId.replace('sku_', '').trim();
                                            const isValidSku = skuId &&
                                                             skuId !== 'sku_' &&
                                                             skuId !== 'sku_undefined' &&
                                                             skuId !== 'sku_null' &&
                                                             skuIdValue !== '' &&
                                                             skuIdValue !== 'undefined' &&
                                                             skuIdValue !== 'null' &&
                                                             skuIdValue.length > 0 &&
                                                             /\d/.test(skuIdValue); // 必须包含数字

                                            if (!isValidSku) {
                                                console.log(`  🚫 跳过无效SKU，ID: ${skuId}, 提取值: "${skuIdValue}"`);
                                                continue;
                                            }

                                            // 检查是否已处理过
                                            if (!window.processedSkuIds.has(skuId)) {
                                                window.processedSkuIds.add(skuId);
                                                uniqueSkuForms.push(skuForm);
                                                console.log(`  ✅ 添加SKU到处理队列，ID: ${skuId}`);
                                            } else {
                                                console.log(`  ❌ 跳过重复SKU，ID: ${skuId}`);
                                            }
                                        }

                                        console.log(`  去重后找到 ${uniqueSkuForms.length} 个唯一SKU`);

                                        for (let i = 0; i < uniqueSkuForms.length; i++) {
                                            console.log(`  处理SKU ${i + 1}/${uniqueSkuForms.length}`);
                                            await processSkuRates(uniqueSkuForms[i], i);

                                            // 移除滚动功能
                                        }

                                        console.log(`✅ 第 ${currentPage}/${totalPages} 页处理完成`);
                                    }

                                    console.log(`🎉 所有 ${totalPages} 页处理完成，准备点击确定按钮`);
                                    
                                    // 点击确定按钮
                                    await clickConfirmButton();
                                    
                                    // 等待当前商品处理完成
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                }
                            }
                        }
                        console.log(`样式二商品处理完成，共处理${processedCount}个`);
                    }

                    // 任务完成总结
                    if (shouldProcessStyle1) {
                        console.log(`
                            任务完成！
                            - 样式一商品：成功处理 ${style1Count} 个
                            总共处理：${style1Count} 个商品
                        `);
                    } else if (shouldProcessStyle2) {
                        if (style1Count > 0 && style2Count > 0) {
                            console.log(`
                                任务完成！
                                - 通过样式二处理方式：成功处理所有 ${style1Count + style2Count} 个商品
                                  （包括 ${style1Count} 个样式一商品 + ${style2Count} 个样式二商品）
                            `);
                        } else {
                            console.log(`
                                任务完成！
                                - 样式二商品：成功处理 ${style2Count} 个
                                总共处理：${style2Count} 个商品
                            `);
                        }
                    }
                }

                // 通用的稳定元素查找函数
                function findRateDropdown(container, rateType) {
                    console.log(`查找${rateType}档费率下拉框...`);

                    const selectors = [
                        // 策略1: 直接通过ID查找（最稳定）
                        `div[id^="toggle_ladder${rateType}Ratio_"]`,
                        // 策略2: 在容器内查找包含特定ID模式的元素
                        `div[id*="ladder${rateType}Ratio"] .mx-trigger`,
                        // 策略3: 通过功能属性查找
                        `div[mx-change*="ladder${rateType}RatioOnChange"] .mx-trigger`,
                        // 策略4: 通过类名和属性组合查找
                        `div.mxgc-dropdown-bd[mx-change*="ladder${rateType}"] .mx-trigger`
                    ];

                    // 如果是低档费率，添加特殊的选择器
                    if (rateType === 'Low') {
                        selectors.unshift('div[id^="toggle_mx_"]'); // 低档费率的特殊ID模式
                    }

                    for (const selector of selectors) {
                        let element = null;

                        try {
                            element = container ? container.querySelector(selector) : document.querySelector(selector);

                            if (element) {
                                console.log(`找到${rateType}档费率下拉框，使用选择器: ${selector}`);
                                return element;
                            }
                        } catch (e) {
                            console.log(`选择器失败: ${selector}, 错误: ${e.message}`);
                        }
                    }

                    console.log(`未找到${rateType}档费率下拉框`);
                    return null;
                }

                // 滚动功能已完全移除

                // 模拟真实的鼠标交互
                function simulateRealMouse(element) {
                    return new Promise((resolve) => {
                        console.log('模拟点击元素:', element.outerHTML);
                        
                        // 鼠标移动到元素上
                        element.dispatchEvent(new MouseEvent('mouseover', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                        }));
                        
                        setTimeout(() => {
                            // 鼠标按下
                            element.dispatchEvent(new MouseEvent('mousedown', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            }));
                            
                            setTimeout(() => {
                                // 鼠标释放和点击
                                element.dispatchEvent(new MouseEvent('mouseup', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }));
                                element.dispatchEvent(new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }));
                                
                                resolve();
                            }, 50);
                        }, 50);
                    });
                }
                
                // 处理单个SKU的费率设置
                async function processSkuRates(skuForm, index) {
                    // 获取当前SKU的标识信息用于调试
                    const row = skuForm.closest('tr');
                    let debugInfo = '';
                    if (row) {
                        const checkbox = row.querySelector('input[type="checkbox"][value]');
                        const skuIdCell = row.children[1];
                        debugInfo = `SKU Value: ${checkbox?.value || 'N/A'}, SKU ID Cell: ${skuIdCell?.textContent?.trim() || 'N/A'}`;
                    }

                    console.log(`🔄 开始处理第 ${index + 1} 个SKU - ${debugInfo}`);

                    // 移除滚动功能，直接处理SKU
                    console.log('开始处理当前SKU...');

                    // 确保页面上没有打开的下拉框 - 增强版清理
                    console.log('清理页面状态...');

                    // 多次点击页面空白处，确保所有下拉框都关闭
                    for (let i = 0; i < 3; i++) {
                        document.body.click();
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    // 额外等待，确保页面状态稳定（最终加快速度）
                    await new Promise(resolve => setTimeout(resolve, 60));

                    // 检查并关闭任何可见的下拉菜单
                    const visibleDropdowns = document.querySelectorAll('div.mx-output-list:not([style*="display: none"])');
                    if (visibleDropdowns.length > 0) {
                        console.log(`发现${visibleDropdowns.length}个未关闭的下拉菜单，强制关闭...`);
                        document.body.click();
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }

                    // 处理高档费率 - 使用新的查找函数
                    const highRateDropdown = findRateDropdown(skuForm, 'High');
                    if (highRateDropdown) {
                        // 获取下拉框ID，支持多种ID格式
                        let dropdownId = '';
                        if (highRateDropdown.id) {
                            dropdownId = highRateDropdown.id.replace('toggle_', '');
                        } else {
                            // 尝试从父元素获取ID
                            const parentWithId = highRateDropdown.closest('div[id*="ladderHighRatio"]');
                            dropdownId = parentWithId ? parentWithId.id : 'ladderHighRatio';
                        }
                        console.log('找到高档费率下拉框，ID:', dropdownId);

                        // 移除滚动功能，直接处理下拉框
                        console.log('准备点击高档费率下拉框...');

                        // 点击下拉框前确保页面状态清洁（增加等待时间）
                        document.body.click();
                        await new Promise(resolve => setTimeout(resolve, 200));

                        await simulateRealMouse(highRateDropdown);
                        
                        // 等待下拉菜单出现并查找选项（等待1秒）
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 使用更精确的策略查找选项
                        let found = false;
                        const targetRate = highRate + '%';

                        // 策略1: 严格匹配当前下拉框的选项
                        console.log(`查找高档费率选项: ${targetRate}, 下拉框ID: ${dropdownId}`);
                        let options = document.querySelectorAll('div.mx-output-link');
                        console.log(`策略1: 找到${options.length}个mx-output-link选项`);

                        for (const option of options) {
                            const rateSpans = option.querySelectorAll('span');
                            const mxClick = option.getAttribute('mx-click');

                            // 严格匹配：必须包含当前下拉框的完整ID
                            if (mxClick && mxClick.includes(dropdownId)) {
                                for (const span of rateSpans) {
                                    if (span.textContent.trim() === targetRate) {
                                        console.log(`严格匹配找到目标选项: ${targetRate}, mx-click:`, mxClick);
                                        await simulateRealMouse(option);

                                        // 等待选项被选中，下拉框自然关闭（增加等待时间）
                                        console.log('等待选项选择生效...');
                                        await new Promise(resolve => setTimeout(resolve, 400));

                                        // 验证选择是否成功（检查下拉框是否显示了选中的值）
                                        const selectedValue = highRateDropdown.textContent;
                                        if (selectedValue.includes(targetRate)) {
                                            console.log(`高档费率选择成功: ${targetRate}`);
                                        } else {
                                            console.log(`高档费率选择可能未生效，当前显示: ${selectedValue}`);
                                        }

                                        found = true;
                                        break;
                                    }
                                }
                                if (found) break;
                            }
                        }

                        // 策略2: 如果严格匹配失败，查找可见的下拉菜单中的选项
                        if (!found) {
                            console.log('策略2: 查找可见下拉菜单中的选项...');

                            // 查找当前可见的下拉菜单容器
                            const visibleDropdowns = document.querySelectorAll('div.mx-output-list:not([style*="display: none"])');
                            console.log(`找到${visibleDropdowns.length}个可见下拉菜单`);

                            for (const dropdown of visibleDropdowns) {
                                const dropdownOptions = dropdown.querySelectorAll('div.mx-output-link');
                                console.log(`当前下拉菜单中有${dropdownOptions.length}个选项`);

                                for (const option of dropdownOptions) {
                                    const rateSpans = option.querySelectorAll('span');
                                    for (const span of rateSpans) {
                                        if (span.textContent.trim() === targetRate) {
                                            console.log(`在可见下拉菜单中找到目标选项: ${targetRate}`);
                                            await simulateRealMouse(option);

                                            // 等待选项被选中，下拉框自然关闭
                                            console.log('等待选项选择生效...');
                                            await new Promise(resolve => setTimeout(resolve, 800));

                                            found = true;
                                            break;
                                        }
                                    }
                                    if (found) break;
                                }
                                if (found) break;
                            }
                        }

                        // 策略3: 最后的备选方案
                        if (!found) {
                            console.log('策略3: 使用最后的备选方案...');
                            // 重新点击下拉框，确保它是活动状态
                            await simulateRealMouse(highRateDropdown);
                            await new Promise(resolve => setTimeout(resolve, 300));

                            // 查找最新出现的选项
                            options = document.querySelectorAll('div.mx-output-link');
                            const recentOptions = Array.from(options).slice(-20); // 取最后20个选项

                            for (const option of recentOptions) {
                                if (option.textContent.includes(targetRate)) {
                                    console.log(`备选方案找到目标选项: ${targetRate}`);
                                    await simulateRealMouse(option);

                                    // 等待选项被选中，下拉框自然关闭
                                    console.log('等待选项选择生效...');
                                    await new Promise(resolve => setTimeout(resolve, 800));

                                    found = true;
                                    break;
                                }
                            }
                        }
                        
                        if (!found) {
                            console.log(`未找到高档费率${highRate}%的选项`);
                            // 如果没找到选项，才手动关闭下拉框
                            console.log('未找到选项，手动关闭下拉框');
                            document.body.click();
                            await new Promise(resolve => setTimeout(resolve, 500));
                        } else {
                            // 选项选择成功，给一个自然的停顿时间
                            console.log('高档费率设置完成，短暂停顿...');
                            await new Promise(resolve => setTimeout(resolve, 600));
                        }
                    }

                    // 在处理高档费率和低档费率之间，模拟用户的自然停顿（最终加快速度）
                    console.log('高档费率处理完成，准备处理低档费率...');
                    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 50)); // 100-150ms的随机延迟

                    // 处理低档费率 - 使用新的查找函数
                    const lowRateDropdown = findRateDropdown(skuForm, 'Low');
                    if (lowRateDropdown) {
                        // 获取下拉框ID，支持多种ID格式
                        let dropdownId = '';
                        if (lowRateDropdown.id) {
                            dropdownId = lowRateDropdown.id.replace('toggle_', '');
                        } else {
                            // 尝试从父元素获取ID或使用默认值
                            const parentWithId = lowRateDropdown.closest('div[mx-change*="ladderLowRatio"]');
                            dropdownId = parentWithId ? parentWithId.id : 'ladderLowRatio';
                        }
                        console.log('找到低档费率下拉框，ID:', dropdownId);

                        // 移除滚动功能，直接处理下拉框
                        console.log('准备点击低档费率下拉框...');

                        // 点击下拉框前确保页面状态清洁
                        document.body.click();
                        await new Promise(resolve => setTimeout(resolve, 300));

                        await simulateRealMouse(lowRateDropdown);
                        
                        // 等待下拉菜单出现并查找选项
                        await new Promise(resolve => setTimeout(resolve, 500));

                        // 使用更精确的策略查找选项
                        let found = false;
                        const targetRate = lowRate + '%';

                        // 策略1: 严格匹配当前下拉框的选项
                        console.log(`查找低档费率选项: ${targetRate}, 下拉框ID: ${dropdownId}`);
                        let options = document.querySelectorAll('div.mx-output-link');
                        console.log(`策略1: 找到${options.length}个mx-output-link选项`);

                        for (const option of options) {
                            const rateSpans = option.querySelectorAll('span');
                            const mxClick = option.getAttribute('mx-click');

                            // 严格匹配：必须包含当前下拉框的完整ID
                            if (mxClick && mxClick.includes(dropdownId)) {
                                for (const span of rateSpans) {
                                    if (span.textContent.trim() === targetRate) {
                                        console.log(`严格匹配找到目标选项: ${targetRate}, mx-click:`, mxClick);
                                        await simulateRealMouse(option);

                                        // 等待选项被选中，下拉框自然关闭
                                        console.log('等待选项选择生效...');
                                        await new Promise(resolve => setTimeout(resolve, 800));

                                        // 验证选择是否成功
                                        const selectedValue = lowRateDropdown.textContent;
                                        if (selectedValue.includes(targetRate)) {
                                            console.log(`低档费率选择成功: ${targetRate}`);
                                        } else {
                                            console.log(`低档费率选择可能未生效，当前显示: ${selectedValue}`);
                                        }

                                        found = true;
                                        break;
                                    }
                                }
                                if (found) break;
                            }
                        }

                        // 策略2: 如果严格匹配失败，查找可见的下拉菜单中的选项
                        if (!found) {
                            console.log('策略2: 查找可见下拉菜单中的选项...');

                            // 查找当前可见的下拉菜单容器
                            const visibleDropdowns = document.querySelectorAll('div.mx-output-list:not([style*="display: none"])');
                            console.log(`找到${visibleDropdowns.length}个可见下拉菜单`);

                            for (const dropdown of visibleDropdowns) {
                                const dropdownOptions = dropdown.querySelectorAll('div.mx-output-link');
                                console.log(`当前下拉菜单中有${dropdownOptions.length}个选项`);

                                for (const option of dropdownOptions) {
                                    const rateSpans = option.querySelectorAll('span');
                                    for (const span of rateSpans) {
                                        if (span.textContent.trim() === targetRate) {
                                            console.log(`在可见下拉菜单中找到目标选项: ${targetRate}`);
                                            await simulateRealMouse(option);
                                            found = true;
                                            break;
                                        }
                                    }
                                    if (found) break;
                                }
                                if (found) break;
                            }
                        }

                        // 策略3: 最后的备选方案
                        if (!found) {
                            console.log('策略3: 使用最后的备选方案...');
                            // 重新点击下拉框，确保它是活动状态
                            await simulateRealMouse(lowRateDropdown);
                            await new Promise(resolve => setTimeout(resolve, 300));

                            // 查找最新出现的选项
                            options = document.querySelectorAll('div.mx-output-link');
                            const recentOptions = Array.from(options).slice(-20); // 取最后20个选项

                            for (const option of recentOptions) {
                                if (option.textContent.includes(targetRate)) {
                                    console.log(`备选方案找到目标选项: ${targetRate}`);
                                    await simulateRealMouse(option);
                                    found = true;
                                    break;
                                }
                            }
                        }
                        
                        if (!found) {
                            console.log(`未找到低档费率${lowRate}%的选项`);
                            // 如果没找到选项，才手动关闭下拉框
                            console.log('未找到选项，手动关闭下拉框');
                            document.body.click();
                            await new Promise(resolve => setTimeout(resolve, 500));
                        } else {
                            // 选项选择成功，给一个自然的停顿时间
                            console.log('低档费率设置完成，短暂停顿...');
                            await new Promise(resolve => setTimeout(resolve, 600));
                        }
                    }

                    // 当前SKU处理完成，模拟用户查看结果的停顿时间（最终加快速度）
                    console.log(`第 ${index + 1} 个SKU处理完成，短暂停顿...`);
                    await new Promise(resolve => setTimeout(resolve, 125 + Math.random() * 60)); // 125-185ms的随机延迟
                }
                
                // 统计总页数 - 动态ID兼容版
                function getTotalPages() {
                    console.log('开始统计总页数...');

                    // 使用更通用的选择器查找分页容器
                    // 替换原来的 'div.dZGuKQbzvy' 为基于属性的更稳定选择器
                    const allPaginations = document.querySelectorAll('div.table-pager-wrapper');
                    console.log(`找到${allPaginations.length}个分页容器`);

                    let targetPagination = null;
                    let maxZIndex = -1;

                    for (const pagination of allPaginations) {
                        const style = window.getComputedStyle(pagination);
                        const rect = pagination.getBoundingClientRect();

                        if (style.display !== 'none' && style.visibility !== 'hidden' &&
                            rect.width > 0 && rect.height > 0) {

                            const zIndex = parseInt(style.zIndex) || 0;
                            const parentZIndex = parseInt(window.getComputedStyle(pagination.parentElement).zIndex) || 0;
                            const effectiveZIndex = Math.max(zIndex, parentZIndex);

                            console.log(`分页器z-index: ${effectiveZIndex}, 位置:`, rect);

                            if (effectiveZIndex >= maxZIndex) {
                                maxZIndex = effectiveZIndex;
                                targetPagination = pagination;
                            }
                        }
                    }

                    if (!targetPagination) {
                        console.log('未找到可见的分页容器，假设只有1页');
                        return 1;
                    }

                    console.log(`选择z-index最高的分页器: ${maxZIndex}`);

                    // 获取分页器的动态ID用于调试
                    const paginationParent = targetPagination.parentElement;
                    let dynamicId = '';
                    if (paginationParent && paginationParent.id) {
                        dynamicId = paginationParent.id;
                        console.log(`分页器动态ID: ${dynamicId}`);
                    }

                    // 查找页码链接 - 使用更通用的选择器
                    // 原来的 'a[mx-click*="ppxkes"]' 保持不变，因为它已经比较通用
                    const pageLinks = targetPagination.querySelectorAll('a[mx-click*="ppxkes"]');
                    console.log(`在目标分页器中找到${pageLinks.length}个页码链接`);

                    if (pageLinks.length === 0) {
                        console.log('未找到页码链接，假设只有1页');
                        return 1;
                    }

                    // 提取最大页码 - 增强版本
                    let maxPage = 1;
                    for (const link of pageLinks) {
                        const mxClick = link.getAttribute('mx-click');
                        console.log(`分析页码链接: ${mxClick}`);

                        // 支持多种页码格式
                        const pagePatterns = [
                            /page:'(\d+)'/,           // page:'2'
                            /page:(\d+)/,             // page:2
                            /\{page:'(\d+)'\}/,       // {page:'2'}
                            /ppxkes\(\{page:'(\d+)'\}\)/ // ppxkes({page:'2'})
                        ];

                        for (const pattern of pagePatterns) {
                            const pageMatch = mxClick.match(pattern);
                            if (pageMatch) {
                                const pageNum = parseInt(pageMatch[1]);
                                console.log(`发现页码: ${pageNum} (使用模式: ${pattern})`);
                                if (pageNum > maxPage) {
                                    maxPage = pageNum;
                                }
                                break; // 找到匹配就跳出内层循环
                            }
                        }
                    }

                    console.log(`最终检测到总页数: ${maxPage}`);
                    return maxPage;
                }

                // 跳转到指定页面 - 更通用的选择器版本
                async function goToPage(targetPage) {
                    console.log(`准备跳转到第 ${targetPage} 页...`);

                    // 使用更通用的选择器查找分页容器
                    // 替换原来的 'div.dZGuKQbzvy' 为基于属性的更稳定选择器
                    const allPaginations = document.querySelectorAll('div.table-pager-wrapper');
                    let targetPagination = null;
                    let maxZIndex = -1;

                    for (const pagination of allPaginations) {
                        const style = window.getComputedStyle(pagination);
                        const rect = pagination.getBoundingClientRect();

                        if (style.display !== 'none' && style.visibility !== 'hidden' &&
                            rect.width > 0 && rect.height > 0) {

                            const zIndex = parseInt(style.zIndex) || 0;
                            const parentZIndex = parseInt(window.getComputedStyle(pagination.parentElement).zIndex) || 0;
                            const effectiveZIndex = Math.max(zIndex, parentZIndex);

                            if (effectiveZIndex >= maxZIndex) {
                                maxZIndex = effectiveZIndex;
                                targetPagination = pagination;
                            }
                        }
                    }

                    if (!targetPagination) {
                        console.log('未找到目标分页器');
                        return false;
                    }

                    // 获取分页器的动态ID
                    const paginationParent = targetPagination.parentElement;
                    let dynamicId = '';
                    if (paginationParent && paginationParent.id) {
                        dynamicId = paginationParent.id;
                        console.log(`检测到分页器动态ID: ${dynamicId}`);
                    }

                    // 查找目标页面的链接 - 使用更通用且准确的选择器
                    let targetLink = null;

                    // 策略1: 使用更准确的属性选择器
                    // 查找 mx-click 属性中包含目标页码的链接
                    const pageLinks = targetPagination.querySelectorAll('a[mx-click*="ppxkes"]');
                    for (const link of pageLinks) {
                        const mxClick = link.getAttribute('mx-click');
                        // 检查 mx-click 是否包含目标页码
                        const pattern = new RegExp(`page:['"]?${targetPage}['"]?`);
                        if (pattern.test(mxClick)) {
                            targetLink = link;
                            console.log(`通过属性匹配找到第${targetPage}页链接:`, mxClick);
                            break;
                        }
                    }

                    // 策略2: 文本内容匹配（备选方案）
                    if (!targetLink) {
                        console.log('尝试通过文本内容查找页面链接...');
                        const allLinks = targetPagination.querySelectorAll('a[href="#"]');
                        for (const link of allLinks) {
                            if (link.textContent.trim() === targetPage.toString()) {
                                targetLink = link;
                                console.log(`通过文本内容找到第${targetPage}页链接`);
                                break;
                            }
                        }
                    }

                    if (targetLink) {
                        console.log(`点击跳转到第 ${targetPage} 页`);
                        await simulateRealMouse(targetLink);
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        return true;
                    }

                    console.log(`未找到第 ${targetPage} 页的链接`);
                    return false;
                }


                
                // 点击确定按钮 - 动态ID兼容版
                async function clickConfirmButton() {
                    console.log('准备点击确定按钮');

                    // 使用多种策略查找确定按钮 - 动态ID兼容版
                    let confirmButton = null;

                    // 策略1: 查找动态ID模式的确定按钮
                    const dynamicButtonSelectors = [
                        'button[data-btn*="_footer_submit_btn"]', // 匹配任何包含_footer_submit_btn的按钮
                        'button[mx-click*="_footer_submit"][mx-click*="ppxkz"]', // 匹配动态ID的mx-click模式
                        'button[data-btn*="cnt_dlg_"][data-btn*="footer_submit_btn"]' // 匹配cnt_dlg_开头的模式
                    ];

                    for (const selector of dynamicButtonSelectors) {
                        try {
                            confirmButton = document.querySelector(selector);
                            if (confirmButton) {
                                console.log(`动态ID匹配找到确定按钮，使用选择器: ${selector}`);
                                break;
                            }
                        } catch (e) {
                            console.log(`动态ID选择器失败: ${selector}`);
                        }
                    }

                    // 策略2: 通用选择器（保持向后兼容）
                    if (!confirmButton) {
                        const buttonSelectors = [
                            'button[data-btn*="footer_submit_btn"]', // 通用submit按钮
                            'button[data-btn*="submit_btn"]', // 简化版本
                            'button[mx-click*="footer_submit"]', // 基于mx-click属性
                            'button[class*="dZGuKQpTW"][class*="dZGuKQpTac-custom-gradient"]', // 基于新版类名组合
                            'button[style*="--mx-btn-custom-color: var(--btn-brand)"]', // 基于样式变量
                            '.mx-dialog-footer button', // 对话框底部按钮
                            'div[class*="footer"] button[type="button"]', // 底部区域按钮
                            'button[class*="gradient"]', // 渐变样式按钮
                            '[role="dialog"] button[type="button"]:last-child' // 对话框最后一个按钮
                        ];

                        for (const selector of buttonSelectors) {
                            try {
                                confirmButton = document.querySelector(selector);
                                if (confirmButton) {
                                    console.log(`通用匹配找到确定按钮，使用选择器: ${selector}`);
                                    break;
                                }
                            } catch (e) {
                                console.log(`确定按钮选择器失败: ${selector}`);
                            }
                        }
                    }

                    // 策略3: 通过XPath查找包含"确定"文本的按钮
                    if (!confirmButton) {
                        try {
                            confirmButton = document.evaluate(
                                "//button[contains(text(), '确定')]",
                                document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null
                            ).singleNodeValue;
                            if (confirmButton) {
                                console.log('通过XPath文本匹配找到确定按钮');
                            }
                        } catch (e) {
                            console.log('XPath查找确定按钮失败');
                        }
                    }

                    // 策略4: 遍历所有按钮并根据文本内容匹配
                    if (!confirmButton) {
                        console.log('尝试通过文本内容查找确定按钮...');
                        const allButtons = document.querySelectorAll('button');
                        for (const button of allButtons) {
                            const buttonText = button.textContent.trim();
                            if (buttonText.includes('确定') ||
                                buttonText.includes('确认') ||
                                buttonText.includes('提交')) {
                                confirmButton = button;
                                console.log('通过文本内容找到确定按钮:', buttonText);
                                break;
                            }
                        }
                    }

                    if (confirmButton) {
                        console.log('准备点击确定按钮');
                        await simulateRealMouse(confirmButton);
                        await new Promise(resolve => setTimeout(resolve, 800));
                        console.log('确定按钮点击完成');
                        return true;
                    }

                    console.log('未找到确定按钮');
                    return false;
                }

                // 执行自动填写
                autoFillRates().then(() => {
                    console.log('开始执行自动填写', { highRate, highCount, lowRate });
                });
            },
            args: [highRate, highCount, lowRate]
        });
    }
});

// 添加事件监听器
document.getElementById('highRate').addEventListener('change', saveSettings);
document.getElementById('highCount').addEventListener('change', saveSettings);
document.getElementById('lowRate').addEventListener('change', saveSettings);
document.getElementById('productInput').addEventListener('input', saveSettings);

// 页面加载时加载设置
document.addEventListener('DOMContentLoaded', loadSettings);
