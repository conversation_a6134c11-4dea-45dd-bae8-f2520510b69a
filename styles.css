body {
    width: 300px;
    padding: 15px;
    font-family: Arial, sans-serif;
}

.container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.section {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
}

.section h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #333;
}

.divider {
    height: 1px;
    background-color: #e8e8e8;
    margin: 5px 0;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.product-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 100px;
    font-family: Arial, sans-serif;
    line-height: 1.5;
}

.product-input:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.action-button {
    padding: 8px 16px;
    background-color: #52c41a;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.action-button:hover {
    background-color: #73d13d;
}

.action-button:active {
    background-color: #389e0d;
}

.rate-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

label {
    min-width: 70px;
}

select, input {
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

input[type="number"] {
    width: 60px;
}

select {
    width: 80px;
}

.start-button {
    width: 100%;
    margin-top: 10px;
    padding: 8px 16px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.start-button:hover {
    background-color: #40a9ff;
}

.start-button:active {
    background-color: #096dd9;
} 