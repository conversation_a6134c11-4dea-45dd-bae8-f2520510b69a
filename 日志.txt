 The PerformanceObserver does not support buffered flag with the entryTypes argument.
install @ index.js:1
Access to fetch at 'https://g.alicdn.com/mm/magix-ports/20250716.125024.855/gallery/mx-gtip/message.js' from origin 'https://myseller.taobao.com' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
The FetchEvent for "https://g.alicdn.com/mm/magix-ports/20250716.125024.855/gallery/mx-gtip/message.js" resulted in a network error response: the promise was rejected.
g.alicdn.com/mm/magix-ports/20250716.125024.855/gallery/mx-gtip/message.js:1  Failed to load resource: net::ERR_FAILED
sw.js:1 Uncaught (in promise) no-response: no-response :: [{"url":"https://g.alicdn.com/mm/magix-ports/20250716.125024.855/gallery/mx-gtip/message.js","error":{}}]
 当前页面找到 142 个SKU容器
   方法5备用: 使用位置标识: page_1_index_0_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_0_1753242163241
   方法2成功: 通过高档费率ID获取SKU ID: sku_953011683199
   ❌ 跳过重复SKU，ID: sku_953011683199
   方法5备用: 使用位置标识: page_1_index_2_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_2_1753242163241
   方法5备用: 使用位置标识: page_1_index_3_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_3_1753242163241
   方法5备用: 使用位置标识: page_1_index_4_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_4_1753242163241
   方法5备用: 使用位置标识: page_1_index_5_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_5_1753242163241
   方法5备用: 使用位置标识: page_1_index_6_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_6_1753242163241
   方法5备用: 使用位置标识: page_1_index_7_1753242163241
   ✅ 添加SKU到处理队列，ID: page_1_index_7_1753242163241
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620127
   ❌ 跳过重复SKU，ID: sku_5874313620127
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620126
   ❌ 跳过重复SKU，ID: sku_5874313620126
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620125
   ❌ 跳过重复SKU，ID: sku_5874313620125
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620124
   ❌ 跳过重复SKU，ID: sku_5874313620124
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620123
   ❌ 跳过重复SKU，ID: sku_5874313620123
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620122
   ❌ 跳过重复SKU，ID: sku_5874313620122
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620128
   ❌ 跳过重复SKU，ID: sku_5874313620128
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620119
   ❌ 跳过重复SKU，ID: sku_5874313620119
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620118
   ❌ 跳过重复SKU，ID: sku_5874313620118
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620117
   ❌ 跳过重复SKU，ID: sku_5874313620117
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620116
   ❌ 跳过重复SKU，ID: sku_5874313620116
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620115
   ❌ 跳过重复SKU，ID: sku_5874313620115
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620121
   ❌ 跳过重复SKU，ID: sku_5874313620121
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620120
   ❌ 跳过重复SKU，ID: sku_5874313620120
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620114
   ❌ 跳过重复SKU，ID: sku_5874313620114
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620113
   ❌ 跳过重复SKU，ID: sku_5874313620113
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620112
   ❌ 跳过重复SKU，ID: sku_5874313620112
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620111
   ❌ 跳过重复SKU，ID: sku_5874313620111
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620110
   ❌ 跳过重复SKU，ID: sku_5874313620110
   方法1成功: 通过checkbox获取SKU ID: sku_5874313620109
   ❌ 跳过重复SKU，ID: sku_5874313620109
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682493
   ❌ 跳过重复SKU，ID: sku_6040794682493
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682492
   ❌ 跳过重复SKU，ID: sku_6040794682492
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682495
   ❌ 跳过重复SKU，ID: sku_6040794682495
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682494
   ❌ 跳过重复SKU，ID: sku_6040794682494
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682497
   ❌ 跳过重复SKU，ID: sku_6040794682497
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682496
   ❌ 跳过重复SKU，ID: sku_6040794682496
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682498
   ❌ 跳过重复SKU，ID: sku_6040794682498
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682485
   ❌ 跳过重复SKU，ID: sku_6040794682485
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682487
   ❌ 跳过重复SKU，ID: sku_6040794682487
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682486
   ❌ 跳过重复SKU，ID: sku_6040794682486
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682489
   ❌ 跳过重复SKU，ID: sku_6040794682489
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682488
   ❌ 跳过重复SKU，ID: sku_6040794682488
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682491
   ❌ 跳过重复SKU，ID: sku_6040794682491
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682490
   ❌ 跳过重复SKU，ID: sku_6040794682490
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682484
   ❌ 跳过重复SKU，ID: sku_6040794682484
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682481
   ❌ 跳过重复SKU，ID: sku_6040794682481
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682480
   ❌ 跳过重复SKU，ID: sku_6040794682480
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682483
   ❌ 跳过重复SKU，ID: sku_6040794682483
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682482
   ❌ 跳过重复SKU，ID: sku_6040794682482
   方法1成功: 通过checkbox获取SKU ID: sku_6040794682479
   ❌ 跳过重复SKU，ID: sku_6040794682479
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223727
   ❌ 跳过重复SKU，ID: sku_6041829223727
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223726
   ❌ 跳过重复SKU，ID: sku_6041829223726
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223725
   ❌ 跳过重复SKU，ID: sku_6041829223725
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223730
   ❌ 跳过重复SKU，ID: sku_6041829223730
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223729
   ❌ 跳过重复SKU，ID: sku_6041829223729
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223728
   ❌ 跳过重复SKU，ID: sku_6041829223728
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223723
   ❌ 跳过重复SKU，ID: sku_6041829223723
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223722
   ❌ 跳过重复SKU，ID: sku_6041829223722
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223721
   ❌ 跳过重复SKU，ID: sku_6041829223721
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223720
   ❌ 跳过重复SKU，ID: sku_6041829223720
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223724
   ❌ 跳过重复SKU，ID: sku_6041829223724
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223719
   ❌ 跳过重复SKU，ID: sku_6041829223719
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223715
   ❌ 跳过重复SKU，ID: sku_6041829223715
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223714
   ❌ 跳过重复SKU，ID: sku_6041829223714
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223713
   ❌ 跳过重复SKU，ID: sku_6041829223713
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223718
   ❌ 跳过重复SKU，ID: sku_6041829223718
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223717
   ❌ 跳过重复SKU，ID: sku_6041829223717
   方法1成功: 通过checkbox获取SKU ID: sku_6041829223716
   ❌ 跳过重复SKU，ID: sku_6041829223716
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484216
   ❌ 跳过重复SKU，ID: sku_5874311484216
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484211
   ❌ 跳过重复SKU，ID: sku_5874311484211
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484212
   ❌ 跳过重复SKU，ID: sku_5874311484212
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484213
   ❌ 跳过重复SKU，ID: sku_5874311484213
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484214
   ❌ 跳过重复SKU，ID: sku_5874311484214
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484215
   ❌ 跳过重复SKU，ID: sku_5874311484215
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484205
   ❌ 跳过重复SKU，ID: sku_5874311484205
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484206
   ❌ 跳过重复SKU，ID: sku_5874311484206
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484207
   ❌ 跳过重复SKU，ID: sku_5874311484207
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484208
   ❌ 跳过重复SKU，ID: sku_5874311484208
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484209
   ❌ 跳过重复SKU，ID: sku_5874311484209
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484210
   ❌ 跳过重复SKU，ID: sku_5874311484210
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484200
   ❌ 跳过重复SKU，ID: sku_5874311484200
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484201
   ❌ 跳过重复SKU，ID: sku_5874311484201
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484202
   ❌ 跳过重复SKU，ID: sku_5874311484202
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484203
   ❌ 跳过重复SKU，ID: sku_5874311484203
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484204
   ❌ 跳过重复SKU，ID: sku_5874311484204
   方法1成功: 通过checkbox获取SKU ID: sku_5874311484199
   ❌ 跳过重复SKU，ID: sku_5874311484199
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775917
   ❌ 跳过重复SKU，ID: sku_6041827775917
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775916
   ❌ 跳过重复SKU，ID: sku_6041827775916
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775919
   ❌ 跳过重复SKU，ID: sku_6041827775919
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775918
   ❌ 跳过重复SKU，ID: sku_6041827775918
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775921
   ❌ 跳过重复SKU，ID: sku_6041827775921
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775920
   ❌ 跳过重复SKU，ID: sku_6041827775920
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775922
   ❌ 跳过重复SKU，ID: sku_6041827775922
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775909
   ❌ 跳过重复SKU，ID: sku_6041827775909
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775911
   ❌ 跳过重复SKU，ID: sku_6041827775911
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775910
   ❌ 跳过重复SKU，ID: sku_6041827775910
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775913
   ❌ 跳过重复SKU，ID: sku_6041827775913
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775912
   ❌ 跳过重复SKU，ID: sku_6041827775912
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775915
   ❌ 跳过重复SKU，ID: sku_6041827775915
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775914
   ❌ 跳过重复SKU，ID: sku_6041827775914
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775905
   ❌ 跳过重复SKU，ID: sku_6041827775905
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775904
   ❌ 跳过重复SKU，ID: sku_6041827775904
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775907
   ❌ 跳过重复SKU，ID: sku_6041827775907
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775906
   ❌ 跳过重复SKU，ID: sku_6041827775906
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775908
   ❌ 跳过重复SKU，ID: sku_6041827775908
   方法1成功: 通过checkbox获取SKU ID: sku_6041827775903
   ❌ 跳过重复SKU，ID: sku_6041827775903
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690098
   ❌ 跳过重复SKU，ID: sku_6040793690098
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690099
   ❌ 跳过重复SKU，ID: sku_6040793690099
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690096
   ❌ 跳过重复SKU，ID: sku_6040793690096
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690097
   ❌ 跳过重复SKU，ID: sku_6040793690097
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690102
   ❌ 跳过重复SKU，ID: sku_6040793690102
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690100
   ❌ 跳过重复SKU，ID: sku_6040793690100
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690101
   ❌ 跳过重复SKU，ID: sku_6040793690101
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690082
   ❌ 跳过重复SKU，ID: sku_6040793690082
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690083
   ❌ 跳过重复SKU，ID: sku_6040793690083
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690086
   ❌ 跳过重复SKU，ID: sku_6040793690086
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690087
   ❌ 跳过重复SKU，ID: sku_6040793690087
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690084
   ❌ 跳过重复SKU，ID: sku_6040793690084
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690085
   ❌ 跳过重复SKU，ID: sku_6040793690085
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690090
   ❌ 跳过重复SKU，ID: sku_6040793690090
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690091
   ❌ 跳过重复SKU，ID: sku_6040793690091
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690088
   ❌ 跳过重复SKU，ID: sku_6040793690088
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690089
   ❌ 跳过重复SKU，ID: sku_6040793690089
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690094
   ❌ 跳过重复SKU，ID: sku_6040793690094
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690095
   ❌ 跳过重复SKU，ID: sku_6040793690095
   方法1成功: 通过checkbox获取SKU ID: sku_6040793690092
   ❌ 跳过重复SKU，ID: sku_6040793690092
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941783
   ✅ 添加SKU到处理队列，ID: sku_5875859941783
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941782
   ✅ 添加SKU到处理队列，ID: sku_5875859941782
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941787
   ✅ 添加SKU到处理队列，ID: sku_5875859941787
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941786
   ✅ 添加SKU到处理队列，ID: sku_5875859941786
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941785
   ✅ 添加SKU到处理队列，ID: sku_5875859941785
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941784
   ✅ 添加SKU到处理队列，ID: sku_5875859941784
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941779
   ✅ 添加SKU到处理队列，ID: sku_5875859941779
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941778
   ✅ 添加SKU到处理队列，ID: sku_5875859941778
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941777
   ✅ 添加SKU到处理队列，ID: sku_5875859941777
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941776
   ✅ 添加SKU到处理队列，ID: sku_5875859941776
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941781
   ✅ 添加SKU到处理队列，ID: sku_5875859941781
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941780
   ✅ 添加SKU到处理队列，ID: sku_5875859941780
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941771
   ✅ 添加SKU到处理队列，ID: sku_5875859941771
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941770
   ✅ 添加SKU到处理队列，ID: sku_5875859941770
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941775
   ✅ 添加SKU到处理队列，ID: sku_5875859941775
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941774
   ✅ 添加SKU到处理队列，ID: sku_5875859941774
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941773
   ✅ 添加SKU到处理队列，ID: sku_5875859941773
   方法1成功: 通过checkbox获取SKU ID: sku_5875859941772
   ✅ 添加SKU到处理队列，ID: sku_5875859941772
   去重后找到 25 个唯一SKU
   处理SKU 1/25
 🔄 开始处理第 1 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 1 个SKU处理完成，短暂停顿...
   处理SKU 2/25
 🔄 开始处理第 2 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 2 个SKU处理完成，短暂停顿...
   处理SKU 3/25
 🔄 开始处理第 3 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 3 个SKU处理完成，短暂停顿...
   处理SKU 4/25
 🔄 开始处理第 4 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 4 个SKU处理完成，短暂停顿...
   处理SKU 5/25
 🔄 开始处理第 5 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 5 个SKU处理完成，短暂停顿...
   处理SKU 6/25
 🔄 开始处理第 6 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 6 个SKU处理完成，短暂停顿...
   处理SKU 7/25
 🔄 开始处理第 7 个SKU - SKU Value: N/A, SKU ID Cell:                   
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 未找到High档费率下拉框
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 未找到Low档费率下拉框
 第 7 个SKU处理完成，短暂停顿...
   处理SKU 8/25
 🔄 开始处理第 8 个SKU - SKU Value: 5875859941783, SKU ID Cell: 5875859941783
 开始处理当前SKU...
 清理页面状态...
 发现234个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941783
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941783" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941783
 策略1: 找到2588个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941783ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941783ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_514611">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512393
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512393" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512393
 策略1: 找到2596个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512393ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512393ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_516428">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 8 个SKU处理完成，短暂停顿...
   处理SKU 9/25
 🔄 开始处理第 9 个SKU - SKU Value: 5875859941782, SKU ID Cell: 5875859941782
 开始处理当前SKU...
 清理页面状态...
 发现236个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941782
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941782" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941782
 策略1: 找到2610个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941782ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941782ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_518056">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512401
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512401" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512401
 策略1: 找到2618个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512401ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512401ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_519862">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 9 个SKU处理完成，短暂停顿...
   处理SKU 10/25
 🔄 开始处理第 10 个SKU - SKU Value: 5875859941787, SKU ID Cell: 5875859941787
 开始处理当前SKU...
 清理页面状态...
 发现238个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941787
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941787" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941787
 策略1: 找到2632个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941787ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941787ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_521516">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512409
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512409" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512409
 策略1: 找到2640个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512409ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512409ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_523348">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 10 个SKU处理完成，短暂停顿...
   处理SKU 11/25
 🔄 开始处理第 11 个SKU - SKU Value: 5875859941786, SKU ID Cell: 5875859941786
 开始处理当前SKU...
 清理页面状态...
 发现240个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941786
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941786" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941786
 策略1: 找到2654个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941786ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941786ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_525028">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512417
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512417" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512417
 策略1: 找到2662个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512417ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512417ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_526886">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 11 个SKU处理完成，短暂停顿...
   处理SKU 12/25
 🔄 开始处理第 12 个SKU - SKU Value: 5875859941785, SKU ID Cell: 5875859941785
 开始处理当前SKU...
 清理页面状态...
 发现242个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941785
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941785" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941785
 策略1: 找到2676个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941785ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941785ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_528592">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512425
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512425" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512425
 策略1: 找到2684个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512425ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512425ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_530476">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 12 个SKU处理完成，短暂停顿...
   处理SKU 13/25
 🔄 开始处理第 13 个SKU - SKU Value: 5875859941784, SKU ID Cell: 5875859941784
 开始处理当前SKU...
 清理页面状态...
 发现244个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941784
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941784" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941784
 策略1: 找到2698个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941784ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941784ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_532208">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512433
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512433" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512433
 策略1: 找到2706个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512433ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512433ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_534118">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 13 个SKU处理完成，短暂停顿...
   处理SKU 14/25
 🔄 开始处理第 14 个SKU - SKU Value: 5875859941779, SKU ID Cell: 5875859941779
 开始处理当前SKU...
 清理页面状态...
 发现246个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941779
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941779" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941779
 策略1: 找到2720个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941779ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941779ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_535876">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512441
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512441" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512441
 策略1: 找到2728个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512441ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512441ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_537812">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 14 个SKU处理完成，短暂停顿...
   处理SKU 15/25
 🔄 开始处理第 15 个SKU - SKU Value: 5875859941778, SKU ID Cell: 5875859941778
 开始处理当前SKU...
 清理页面状态...
 发现248个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941778
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941778" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941778
 策略1: 找到2742个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941778ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941778ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_539596">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512449
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512449" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512449
 策略1: 找到2750个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512449ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512449ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_541558">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 15 个SKU处理完成，短暂停顿...
   处理SKU 16/25
 🔄 开始处理第 16 个SKU - SKU Value: 5875859941777, SKU ID Cell: 5875859941777
 开始处理当前SKU...
 清理页面状态...
 发现250个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941777
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941777" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941777
 策略1: 找到2764个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941777ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941777ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_543368">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512457
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512457" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512457
 策略1: 找到2772个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512457ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512457ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_545356">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 16 个SKU处理完成，短暂停顿...
   处理SKU 17/25
 🔄 开始处理第 17 个SKU - SKU Value: 5875859941776, SKU ID Cell: 5875859941776
 开始处理当前SKU...
 清理页面状态...
 发现252个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941776
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941776" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941776
 策略1: 找到2786个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941776ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941776ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_547192">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512465
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512465" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512465
 策略1: 找到2794个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512465ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512465ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_549206">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 17 个SKU处理完成，短暂停顿...
   处理SKU 18/25
 🔄 开始处理第 18 个SKU - SKU Value: 5875859941781, SKU ID Cell: 5875859941781
 开始处理当前SKU...
 清理页面状态...
 发现254个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941781
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941781" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941781
 策略1: 找到2808个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941781ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941781ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_551068">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512473
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512473" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512473
 策略1: 找到2816个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512473ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512473ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_553108">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 18 个SKU处理完成，短暂停顿...
   处理SKU 19/25
 🔄 开始处理第 19 个SKU - SKU Value: 5875859941780, SKU ID Cell: 5875859941780
 开始处理当前SKU...
 清理页面状态...
 发现256个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941780
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941780" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941780
 策略1: 找到2830个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941780ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941780ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_554996">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512481
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512481" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512481
 策略1: 找到2838个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512481ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512481ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_557062">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 19 个SKU处理完成，短暂停顿...
   处理SKU 20/25
 🔄 开始处理第 20 个SKU - SKU Value: 5875859941771, SKU ID Cell: 5875859941771
 开始处理当前SKU...
 清理页面状态...
 发现258个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941771
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941771" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941771
 策略1: 找到2852个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941771ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941771ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_558976">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512489
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512489" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512489
 策略1: 找到2860个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512489ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512489ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_561068">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 20 个SKU处理完成，短暂停顿...
   处理SKU 21/25
 🔄 开始处理第 21 个SKU - SKU Value: 5875859941770, SKU ID Cell: 5875859941770
 开始处理当前SKU...
 清理页面状态...
 发现260个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941770
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941770" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941770
 策略1: 找到2874个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941770ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941770ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_563008">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512497
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512497" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512497
 策略1: 找到2882个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512497ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512497ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_565126">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 21 个SKU处理完成，短暂停顿...
   处理SKU 22/25
 🔄 开始处理第 22 个SKU - SKU Value: 5875859941775, SKU ID Cell: 5875859941775
 开始处理当前SKU...
 清理页面状态...
 发现262个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941775
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941775" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941775
 策略1: 找到2896个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941775ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941775ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_567092">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512505
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512505" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512505
 策略1: 找到2904个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512505ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512505ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_569236">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 22 个SKU处理完成，短暂停顿...
   处理SKU 23/25
 🔄 开始处理第 23 个SKU - SKU Value: 5875859941774, SKU ID Cell: 5875859941774
 开始处理当前SKU...
 清理页面状态...
 发现264个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941774
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941774" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941774
 策略1: 找到2918个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941774ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941774ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_571228">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512513
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512513" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512513
 策略1: 找到2926个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512513ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512513ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_573398">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 23 个SKU处理完成，短暂停顿...
   处理SKU 24/25
 🔄 开始处理第 24 个SKU - SKU Value: 5875859941773, SKU ID Cell: 5875859941773
 开始处理当前SKU...
 清理页面状态...
 发现266个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941773
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941773" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941773
 策略1: 找到2940个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941773ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941773ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_575416">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512521
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512521" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512521
 策略1: 找到2948个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512521ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512521ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_577612">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 24 个SKU处理完成，短暂停顿...
   处理SKU 25/25
 🔄 开始处理第 25 个SKU - SKU Value: 5875859941772, SKU ID Cell: 5875859941772
 开始处理当前SKU...
 清理页面状态...
 发现268个未关闭的下拉菜单，强制关闭...
 查找High档费率下拉框...
 找到High档费率下拉框，使用选择器: div[id^="toggle_ladderHighRatio_"]
 找到高档费率下拉框，ID: ladderHighRatio_5875859941772
 准备点击高档费率下拉框...
 模拟点击元素: <div id="toggle_ladderHighRatio_5875859941772" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找高档费率选项: 30%, 下拉框ID: ladderHighRatio_5875859941772
 策略1: 找到2962个mx-output-link选项
 严格匹配找到目标选项: 30%, mx-click: mx_output_ladderHighRatio_5875859941772ppxkt({item:'5'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_ladderHighRatio_5875859941772ppxkt({item:'5'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=d4c6204243000"><span mxa="dZGuKQpTz:q" class="ellipsis">30%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E8%B4%B9%E7%8E%87%E8%BF%87%E4%BD%8E&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=1" id="mx_579656">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">费率过低</span></span>  </span>  </div>
 等待选项选择生效...
 高档费率选择成功: 30%
 高档费率设置完成，短暂停顿...
 高档费率处理完成，准备处理低档费率...
 查找Low档费率下拉框...
 找到Low档费率下拉框，使用选择器: div[id^="toggle_mx_"]
 找到低档费率下拉框，ID: mx_512529
 准备点击低档费率下拉框...
 模拟点击元素: <div id="toggle_mx_512529" class="mx-trigger  "><span mxa="dZGuKQpTx:d" class="mx-trigger-label"> <span mxa="dZGuKQpTx:g" class="mx-trigger-placeholder">请选择</span></span><i mxs="dZGuKQpTx:_" class="mc-iconfont mx-trigger-arrow"></i></div>
 查找低档费率选项: 20%, 下拉框ID: mx_512529
 策略1: 找到2970个mx-output-link选项
 严格匹配找到目标选项: 20%, mx-click: mx_output_mx_512529ppxkt({item:'2'})
 模拟点击元素: <div mxv="" class="mx-output-link display-flex align-items-center" mx-click="mx_output_mx_512529ppxkt({item:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=de270b24d2000"><span mxa="dZGuKQpTz:q" class="ellipsis">20%</span> <span mxv="parents" class="ml4 mxgc-effects-icon" mx-view="ppxk/gallery/mx-effects/icon?mode=opacity&amp;type=error&amp;color=&amp;content=%E6%8E%A8%E8%8D%90&amp;tip=&amp;tipView=&amp;tipWidth=&amp;tipData=3" id="mx_581878">  <span class="dZGuKQpTiP dZGuKQpTiQ-opacity dZGuKQpTiR-error" style="background-color: rgba(255, 0, 0, 0.1);border: 0 none;color: rgba(255,0,0,1)"><span class="dZGuKQpTiM" style="">推荐</span></span>  </span>  </div>
 等待选项选择生效...
 低档费率选择成功: 20%
 低档费率设置完成，短暂停顿...
 第 25 个SKU处理完成，短暂停顿...
 ✅ 第 1/1 页处理完成
 🎉 所有 1 页处理完成，准备点击确定按钮
 准备点击确定按钮
 找到确定按钮，使用选择器: button[data-btn*="footer_submit_btn"]
 准备点击确定按钮
 模拟点击元素: <button mxv="popData" type="button" data-btn="cnt_dlg_18204_footer_submit_btn" style="--mx-btn-custom-color: var(--btn-brand);--mx-btn-custom-color-gradient: var(--btn-brand-gradient);--mx-btn-custom-color-text: var(--btn-brand-text);--mx-btn-custom-shadow: var(--btn-brand-shadow);--mx-btn-custom-color-hover: var(--btn-brand-hover);--mx-btn-custom-color-gradient-hover: var(--btn-brand-gradient-hover);--mx-btn-custom-color-text-hover: var(--btn-brand-text-hover);--mx-btn-custom-shadow-hover: var(--btn-brand-shadow-hover);--mx-comp-expand-amin-color: #fff;  width: 100%; " class="dZGuKQpTW   dZGuKQpTac-normal dZGuKQpTac-custom-gradient  " mx-click="cnt_dlg_18204_footer_submitppxkz()" mx-comp-expand-amin="animend"><span class="dZGuKQpTX ">确定</span></button>
index.js:18 Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'ok')
    at index.js:18:1753
    at Array.forEach (<anonymous>)
    at index.js:18:1734
 确定按钮点击完成
 样式二商品处理完成，共处理7个
 
                                任务完成！
                                - 通过样式二处理方式：成功处理所有 8 个商品
                                  （包括 1 个样式一商品 + 7 个样式二商品）
                            
 开始执行自动填写 Object
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text         单  
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text         单  
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text         单  
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text         单  
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text         单  
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text         单  
index.js:1 window?.getSelection() Selection
index.js:1 window?.getSelection() text 
