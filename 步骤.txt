这是一个浏览器扩展，本来运行良好，但是由于网站更新，它有一个步骤出现了失效——

多档费率商品在出现弹窗，填写好费率，需要调整到第二页继续填写的时候，跳转页数出现了失败。你可以阅读日志.txt

以下是我的要求，请完整阅读整个扩展的代码，然后阅读以下的商品页数的审查元素，分析一下，当前的代码为什么出现失效？需要说明的时，上次我们已经修复过这个问题，但是网站的商品页数审查元素可能有动态变化的地方，所以今天运行再次失效


以下是不同商品的弹窗的页数的审查元素——
<div mxv="page" mxo="cnt_dlg_425003" mxe="cnt_dlg_425003_f" mxc="[{p:'page',a:'page'}]" class="table-pager-wrapper" mx-change="cnt_dlg_425003mxChangePage()" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89b" mx-view="ppxk/gallery/mx-pagination/index?total=21&amp;size=20&amp;page=152&amp;sizesChange=false" id="mx_425223"><div mxv="" class="clearfix dZGuKQpTvy" style="--mx-pagination-border: var(--btn-border);--mx-pagination-bg: var(--btn-bg);--mx-pagination-color: var(--btn-text);--mx-pagination-border-hover: var(--btn-border-hover);--mx-pagination-bg-hover: var(--btn-bg-hover);--mx-pagination-color-hover: var(--btn-text-hover);--mx-pagination-border-active: var(--color-brand);--mx-pagination-bg-active: var(--color-brand);--mx-pagination-color-active: #fff;--mx-pagination-text-color: var(--mx-pagination-color);--mx-pagination-text-color-hover: var(--mx-pagination-color-hover)">  <span mxv="" mxa="dZGuKQpT::b" class="dZGuKQpTvD mr8"> &nbsp;20条/页&nbsp; </span>  <span class="dZGuKQpTvH" mx-click="mx_425223ppxkes({page:'0'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bprev" mx-view="ppxk/gallery/mx-btn/index?mode=secondary&amp;disabled=true" id="mx_427150"><button mxv="popData" type="button" data-btn="mx_427150_btn" style="--mx-btn-custom-color-border: var(--btn-border);--mx-btn-custom-color-border-hover: var(--btn-border-hover);--mx-btn-custom-color: var(--btn-bg);--mx-btn-custom-color-hover: var(--btn-bg-hover);--mx-btn-custom-color-text: var(--btn-text);--mx-btn-custom-color-text-hover: var(--btn-text-hover);  width: 100%; " class="dZGuKQpTW   dZGuKQpTae  dZGuKQpTac-normal dZGuKQpTac-custom  " disabled="true" mx-click="mx_427150ppxkz()"><span class="dZGuKQpTX "><i mxs="dZGuKQpT::_" class="mx-iconfont dZGuKQpTvz"></i>&nbsp;上一页</span></button>   <span mxs="dZGuKQpTe:c" mx-click="mx_427150ppxkh()" class="dZGuKQpTag"></span>  </span>   <a class="dZGuKQpTvB  dZGuKQpTvF " href="#" mx-click="mx_425223ppxkes({page:'1'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bn1" data-spm-anchor-id="a21dvs.29302593.ppxk_views_pages_qn-yytg-common_sign_set-sku.dfec2c89bn1">1</a><a class="dZGuKQpTvB " href="#" mx-click="mx_425223ppxkes({page:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bn2;userId=undefined&amp;memberId=undefined&amp;bpUrl=https%3A%2F%2Fmyseller.taobao.com%2Fhome.htm%2Fqianniu-yingxiaotuoguan%2F&amp;uuid=null&amp;sid=njlfniarfcg&amp;time=1753242312071&amp;sessionId=njlfniarfcg" data-spm-anchor-id="a21dvs.29302593.ppxk_views_pages_qn-yytg-common_sign_set-sku.dfec2c89bn2">2</a>   <span class="dZGuKQpTvH" mx-click="mx_425223ppxkes({page:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bnext" mx-view="ppxk/gallery/mx-btn/index?mode=secondary&amp;disabled=false" id="mx_427151"><button mxv="popData" type="button" data-btn="mx_427151_btn" style="--mx-btn-custom-color-border: var(--btn-border);--mx-btn-custom-color-border-hover: var(--btn-border-hover);--mx-btn-custom-color: var(--btn-bg);--mx-btn-custom-color-hover: var(--btn-bg-hover);--mx-btn-custom-color-text: var(--btn-text);--mx-btn-custom-color-text-hover: var(--btn-text-hover);  width: 100%; " class="dZGuKQpTW   dZGuKQpTac-normal dZGuKQpTac-custom  " mx-click="mx_427151ppxkz()"><span class="dZGuKQpTX ">下一页&nbsp;<i mxs="dZGuKQpT::b" class="mx-iconfont dZGuKQpTvz dZGuKQpTvA"></i></span></button>     </span>  <span mxv="" mxa="dZGuKQpT::g" class="dZGuKQpTvD" style="margin-left: 12px;"><span mxs="dZGuKQpT::c" class="mr4">跳至</span><input class="input dZGuKQpTvx" id="mx_425223_jump_input" value="2" mx-keyup="mx_425223ppxkeu()" mx-change="mx_425223ppxkh()" mx-focusin="mx_425223ppxkh()" mx-focusout="mx_425223ppxkh()" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bji"><span mxs="dZGuKQpT::d" class="ml4">页</span></span><span class="dZGuKQpTvH" mx-click="mx_425223ppxkeu()" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bjt" mx-view="ppxk/gallery/mx-btn/index?mode=secondary&amp;content=%E8%B7%B3%E8%BD%AC" id="mx_427152"><button mxv="popData" type="button" data-btn="mx_427152_btn" style="--mx-btn-custom-color-border: var(--btn-border);--mx-btn-custom-color-border-hover: var(--btn-border-hover);--mx-btn-custom-color: var(--btn-bg);--mx-btn-custom-color-hover: var(--btn-bg-hover);--mx-btn-custom-color-text: var(--btn-text);--mx-btn-custom-color-text-hover: var(--btn-text-hover);  width: 100%; " class="dZGuKQpTW   dZGuKQpTac-normal dZGuKQpTac-custom  " mx-click="mx_427152ppxkz()"><span class="dZGuKQpTX ">跳转</span></button>     </span> </div></div>

第二个商品——
<div mxv="page" mxo="cnt_dlg_337678" mxe="cnt_dlg_337678_f" mxc="[{p:'page',a:'page'}]" class="table-pager-wrapper" mx-change="cnt_dlg_337678mxChangePage()" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89b" mx-view="ppxk/gallery/mx-pagination/index?total=21&amp;size=20&amp;page=3484&amp;sizesChange=false" id="mx_337898"><div mxv="" class="clearfix dZGuKQpTvy" style="--mx-pagination-border: var(--btn-border);--mx-pagination-bg: var(--btn-bg);--mx-pagination-color: var(--btn-text);--mx-pagination-border-hover: var(--btn-border-hover);--mx-pagination-bg-hover: var(--btn-bg-hover);--mx-pagination-color-hover: var(--btn-text-hover);--mx-pagination-border-active: var(--color-brand);--mx-pagination-bg-active: var(--color-brand);--mx-pagination-color-active: #fff;--mx-pagination-text-color: var(--mx-pagination-color);--mx-pagination-text-color-hover: var(--mx-pagination-color-hover)">  <span mxv="" mxa="dZGuKQpT::b" class="dZGuKQpTvD mr8"> &nbsp;20条/页&nbsp; </span>  <span class="dZGuKQpTvH" mx-click="mx_337898ppxkes({page:'1'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bprev" mx-view="ppxk/gallery/mx-btn/index?mode=secondary&amp;disabled=false" id="mx_339825"><button mxv="popData" type="button" data-btn="mx_339825_btn" style="--mx-btn-custom-color-border: var(--btn-border);--mx-btn-custom-color-border-hover: var(--btn-border-hover);--mx-btn-custom-color: var(--btn-bg);--mx-btn-custom-color-hover: var(--btn-bg-hover);--mx-btn-custom-color-text: var(--btn-text);--mx-btn-custom-color-text-hover: var(--btn-text-hover);  width: 100%; " class="dZGuKQpTW   dZGuKQpTac-normal dZGuKQpTac-custom  " mx-click="mx_339825ppxkz()"><span class="dZGuKQpTX "><i mxs="dZGuKQpT::_" class="mx-iconfont dZGuKQpTvz"></i>&nbsp;上一页</span></button>     </span>   <a class="dZGuKQpTvB " href="#" mx-click="mx_337898ppxkes({page:'1'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bn1" data-spm-anchor-id="a21dvs.29302593.ppxk_views_pages_qn-yytg-common_sign_set-sku.dfec2c89bn1">1</a><a class="dZGuKQpTvB  dZGuKQpTvF " href="#" mx-click="mx_337898ppxkes({page:'2'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bn2;userId=undefined&amp;memberId=undefined&amp;bpUrl=https%3A%2F%2Fmyseller.taobao.com%2Fhome.htm%2Fqianniu-yingxiaotuoguan%2F&amp;uuid=null&amp;sid=njlfniarfcg&amp;time=1753242343557&amp;sessionId=njlfniarfcg" data-spm-anchor-id="a21dvs.29302593.ppxk_views_pages_qn-yytg-common_sign_set-sku.dfec2c89bn2">2</a>   <span class="dZGuKQpTvH" mx-click="mx_337898ppxkes({page:'3'})" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bnext" mx-view="ppxk/gallery/mx-btn/index?mode=secondary&amp;disabled=true" id="mx_339826"><button mxv="popData" type="button" data-btn="mx_339826_btn" style="--mx-btn-custom-color-border: var(--btn-border);--mx-btn-custom-color-border-hover: var(--btn-border-hover);--mx-btn-custom-color: var(--btn-bg);--mx-btn-custom-color-hover: var(--btn-bg-hover);--mx-btn-custom-color-text: var(--btn-text);--mx-btn-custom-color-text-hover: var(--btn-text-hover);  width: 100%; " class="dZGuKQpTW   dZGuKQpTae  dZGuKQpTac-normal dZGuKQpTac-custom  " mx-click="mx_339826ppxkz()" disabled="true"><span class="dZGuKQpTX ">下一页&nbsp;<i mxs="dZGuKQpT::b" class="mx-iconfont dZGuKQpTvz dZGuKQpTvA"></i></span></button>   <span mxs="dZGuKQpTe:c" mx-click="mx_339826ppxkh()" class="dZGuKQpTag"></span>  </span>  <span mxv="" mxa="dZGuKQpT::g" class="dZGuKQpTvD" style="margin-left: 12px;"><span mxs="dZGuKQpT::c" class="mr4">跳至</span><input class="input dZGuKQpTvx" id="mx_337898_jump_input" value="2" mx-keyup="mx_337898ppxkeu()" mx-change="mx_337898ppxkh()" mx-focusin="mx_337898ppxkh()" mx-focusout="mx_337898ppxkh()" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bji"><span mxs="dZGuKQpT::d" class="ml4">页</span></span><span class="dZGuKQpTvH" mx-click="mx_337898ppxkeu()" data-spm-click="gostr=/alimama_bp.4.1;locaid=dfec2c89bjt" mx-view="ppxk/gallery/mx-btn/index?mode=secondary&amp;content=%E8%B7%B3%E8%BD%AC" id="mx_339827"><button mxv="popData" type="button" data-btn="mx_339827_btn" style="--mx-btn-custom-color-border: var(--btn-border);--mx-btn-custom-color-border-hover: var(--btn-border-hover);--mx-btn-custom-color: var(--btn-bg);--mx-btn-custom-color-hover: var(--btn-bg-hover);--mx-btn-custom-color-text: var(--btn-text);--mx-btn-custom-color-text-hover: var(--btn-text-hover);  width: 100%; " class="dZGuKQpTW   dZGuKQpTac-normal dZGuKQpTac-custom  " mx-click="mx_339827ppxkz()"><span class="dZGuKQpTX ">跳转</span></button>     </span> </div></div>


同时，检查一下 确定 按钮的点击，这是确定按钮的审查元素，看看确定按钮有没有出现变化——
<button mxv="popData" type="button" data-btn="cnt_dlg_106931_footer_submit_btn" style="--mx-btn-custom-color: var(--btn-brand);--mx-btn-custom-color-gradient: var(--btn-brand-gradient);--mx-btn-custom-color-text: var(--btn-brand-text);--mx-btn-custom-shadow: var(--btn-brand-shadow);--mx-btn-custom-color-hover: var(--btn-brand-hover);--mx-btn-custom-color-gradient-hover: var(--btn-brand-gradient-hover);--mx-btn-custom-color-text-hover: var(--btn-brand-text-hover);--mx-btn-custom-shadow-hover: var(--btn-brand-shadow-hover);--mx-comp-expand-amin-color: #fff;  width: 100%; " class="dZGuKQpTW   dZGuKQpTac-normal dZGuKQpTac-custom-gradient  " mx-click="cnt_dlg_106931_footer_submitppxkz()"><span class="dZGuKQpTX " data-spm-anchor-id="a21dvs.29302593.ppxk_views_pages_qn-yytg-common_sign_set-sku.i289.42f4645eqfaIJB">确定</span></button>

我的要求是，确保我们的选择器兼容这种情况，究竟是不是审查元素本身存在动态变化的部分？